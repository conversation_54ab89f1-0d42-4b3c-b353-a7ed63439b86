"""
信使角色模块 - 实现信使的移动和咏唱机制
"""

from typing import Tuple, Optional
from enum import Enum
from .environment import Team, GameEnvironment


class ActionType(Enum):
    """行动类型枚举"""
    MOVE = "move"
    CHANT = "chant"
    WAIT = "wait"


class Action:
    """行动类"""
    
    def __init__(self, action_type: ActionType, direction: Optional[Tuple[int, int]] = None):
        """
        初始化行动
        
        Args:
            action_type: 行动类型
            direction: 移动方向（仅移动时需要）
        """
        self.action_type = action_type
        self.direction = direction
    
    def __str__(self) -> str:
        if self.action_type == ActionType.MOVE and self.direction:
            return f"移动到方向 {self.direction}"
        elif self.action_type == ActionType.CHANT:
            return "咏唱"
        else:
            return "等待"


class Messenger:
    """信使类 - 游戏中的角色单位"""
    
    def __init__(self, messenger_id: int, team: Team, x: int, y: int, max_chant_energy: int = 20):
        """
        初始化信使
        
        Args:
            messenger_id: 信使ID
            team: 所属团队
            x, y: 初始位置
            max_chant_energy: 最大咏唱能量
        """
        self.messenger_id = messenger_id
        self.team = team
        self.x = x
        self.y = y
        self.max_chant_energy = max_chant_energy
        self.current_chant_energy = max_chant_energy
        self.total_moves = 0
        self.total_chants = 0
        self.penalty_score = 0.0
    
    @property
    def position(self) -> Tuple[int, int]:
        """获取当前位置"""
        return (self.x, self.y)
    
    @property
    def can_chant(self) -> bool:
        """检查是否还能咏唱"""
        return self.current_chant_energy > 0
    
    def move(self, environment: GameEnvironment, dx: int, dy: int) -> bool:
        """
        移动信使
        
        Args:
            environment: 游戏环境
            dx, dy: 移动方向
            
        Returns:
            是否移动成功
        """
        new_x = self.x + dx
        new_y = self.y + dy
        
        if environment.is_valid_position(new_x, new_y):
            self.x = new_x
            self.y = new_y
            self.total_moves += 1
            self.penalty_score += 0.1  # 移动惩罚
            return True
        
        return False
    
    def chant(self, environment: GameEnvironment) -> bool:
        """
        在当前位置咏唱
        
        Args:
            environment: 游戏环境
            
        Returns:
            是否咏唱成功
        """
        if not self.can_chant:
            return False
        
        # 根据团队类型修改能量
        if self.team == Team.RED:
            environment.modify_energy(self.x, self.y, 1)
        elif self.team == Team.BLUE:
            environment.modify_energy(self.x, self.y, -1)
        
        self.current_chant_energy -= 1
        self.total_chants += 1
        self.penalty_score += 0.2  # 咏唱惩罚
        return True
    
    def execute_action(self, environment: GameEnvironment, action: Action) -> bool:
        """
        执行指定行动
        
        Args:
            environment: 游戏环境
            action: 要执行的行动
            
        Returns:
            是否执行成功
        """
        if action.action_type == ActionType.MOVE:
            if action.direction:
                dx, dy = action.direction
                return self.move(environment, dx, dy)
            return False
        elif action.action_type == ActionType.CHANT:
            return self.chant(environment)
        elif action.action_type == ActionType.WAIT:
            return True  # 等待总是成功
        
        return False
    
    def get_valid_moves(self, environment: GameEnvironment) -> list[Tuple[int, int]]:
        """
        获取所有有效的移动方向
        
        Args:
            environment: 游戏环境
            
        Returns:
            有效移动方向列表
        """
        valid_moves = []
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # 上下左右
        
        for dx, dy in directions:
            new_x = self.x + dx
            new_y = self.y + dy
            if environment.is_valid_position(new_x, new_y):
                valid_moves.append((dx, dy))
        
        return valid_moves
    
    def get_available_actions(self, environment: GameEnvironment) -> list[Action]:
        """
        获取所有可用的行动
        
        Args:
            environment: 游戏环境
            
        Returns:
            可用行动列表
        """
        actions = []
        
        # 添加移动行动
        for direction in self.get_valid_moves(environment):
            actions.append(Action(ActionType.MOVE, direction))
        
        # 添加咏唱行动（如果还有咏唱能量）
        if self.can_chant:
            actions.append(Action(ActionType.CHANT))
        
        # 添加等待行动
        actions.append(Action(ActionType.WAIT))
        
        return actions
    
    def reset(self, x: int, y: int) -> None:
        """
        重置信使到初始状态
        
        Args:
            x, y: 重置位置
        """
        self.x = x
        self.y = y
        self.current_chant_energy = self.max_chant_energy
        self.total_moves = 0
        self.total_chants = 0
        self.penalty_score = 0.0
    
    def get_state_dict(self) -> dict:
        """获取信使状态字典（用于AI观察）"""
        return {
            'id': self.messenger_id,
            'team': self.team.value,
            'position': (self.x, self.y),
            'chant_energy': self.current_chant_energy,
            'max_chant_energy': self.max_chant_energy,
            'total_moves': self.total_moves,
            'total_chants': self.total_chants,
            'penalty_score': self.penalty_score,
            'can_chant': self.can_chant
        }
    
    def __str__(self) -> str:
        """返回信使的字符串表示"""
        team_symbol = "🔴" if self.team == Team.RED else "🔵"
        return f"{team_symbol}信使{self.messenger_id} 位置:({self.x},{self.y}) 咏唱能量:{self.current_chant_energy}/{self.max_chant_energy}"
    
    def __repr__(self) -> str:
        return f"Messenger(id={self.messenger_id}, team={self.team.value}, pos=({self.x},{self.y}), energy={self.current_chant_energy})"
