#!/usr/bin/env python3
"""
晶格信使游戏 - 控制台版本（无需pygame）
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from lattice_messengers import (
        LatticeMessengersGame, 
        RandomAgent, 
        GreedyAgent, 
        HeuristicAgent,
        Team
    )
    
    def main():
        print("🎮 晶格信使 - 控制台版本")
        print("=" * 50)
        
        # 创建游戏
        game = LatticeMessengersGame()
        
        # 设置AI代理
        red_agent = HeuristicAgent(Team.RED)
        blue_agent = GreedyAgent(Team.BLUE)
        game.set_ai_agents(red_agent, blue_agent)
        
        print(f"红队代理: {red_agent.agent_name}")
        print(f"蓝队代理: {blue_agent.agent_name}")
        print()
        
        # 运行游戏
        result = game.run_game(verbose=True)
        
        print("\n📊 游戏统计:")
        print(f"总回合数: {result['total_turns']}")
        print(f"红队最长链: {int(result['red_score'] ** 0.5) if result['red_score'] > 0 else 0}")
        print(f"蓝队最长链: {int(result['blue_score'] ** 0.5) if result['blue_score'] > 0 else 0}")
        
        # 显示最终环境状态
        print("\n🗺️ 最终环境状态:")
        print(game.environment)
        
        return result

    if __name__ == "__main__":
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请先安装必要的依赖包:")
    print("pip install numpy matplotlib colorama -i https://pypi.douban.com/simple/")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
