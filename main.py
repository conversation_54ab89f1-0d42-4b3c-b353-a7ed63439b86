#!/usr/bin/env python3
"""
晶格信使游戏主程序
运行示例和演示
"""

import sys
import argparse
from lattice_messengers import (
    LatticeMessengersGame, 
    GameVisualizer,
    RandomAgent, 
    GreedyAgent, 
    HeuristicAgent,
    Team
)


def run_console_game():
    """运行控制台版本的游戏"""
    print("🎮 晶格信使 - 控制台版本")
    print("=" * 50)
    
    # 创建游戏
    game = LatticeMessengersGame()
    
    # 设置AI代理
    red_agent = HeuristicAgent(Team.RED)
    blue_agent = GreedyAgent(Team.BLUE)
    game.set_ai_agents(red_agent, blue_agent)
    
    print(f"红队代理: {red_agent.agent_name}")
    print(f"蓝队代理: {blue_agent.agent_name}")
    print()
    
    # 运行游戏
    result = game.run_game(verbose=True)
    
    print("\n📊 游戏统计:")
    print(f"总回合数: {result['total_turns']}")
    print(f"红队最长链: {int(result['red_score'] ** 0.5) if result['red_score'] > 0 else 0}")
    print(f"蓝队最长链: {int(result['blue_score'] ** 0.5) if result['blue_score'] > 0 else 0}")
    
    return result


def run_visual_game():
    """运行可视化版本的游戏"""
    print("🎮 晶格信使 - 可视化版本")
    print("启动pygame窗口...")
    
    try:
        # 创建游戏
        game = LatticeMessengersGame()
        
        # 设置AI代理
        red_agent = HeuristicAgent(Team.RED)
        blue_agent = GreedyAgent(Team.BLUE)
        game.set_ai_agents(red_agent, blue_agent)
        
        # 创建可视化器并运行
        visualizer = GameVisualizer(game)
        visualizer.run()
        
    except ImportError:
        print("❌ 错误: 未安装pygame")
        print("请运行: pip install pygame")
        return None
    except Exception as e:
        print(f"❌ 错误: {e}")
        return None


def run_benchmark():
    """运行性能基准测试"""
    print("🏁 性能基准测试")
    print("=" * 50)
    
    agents = {
        "随机代理": RandomAgent,
        "贪心代理": GreedyAgent,
        "启发式代理": HeuristicAgent
    }
    
    num_games = 100
    results = {}
    
    # 测试不同AI代理组合
    for red_name, RedAgent in agents.items():
        for blue_name, BlueAgent in agents.items():
            print(f"测试: {red_name} vs {blue_name}")
            
            red_wins = 0
            blue_wins = 0
            ties = 0
            total_red_score = 0
            total_blue_score = 0
            
            for i in range(num_games):
                game = LatticeMessengersGame()
                red_agent = RedAgent(Team.RED)
                blue_agent = BlueAgent(Team.BLUE)
                game.set_ai_agents(red_agent, blue_agent)
                
                result = game.run_game(verbose=False)
                
                total_red_score += result['red_score']
                total_blue_score += result['blue_score']
                
                if result['winner'] == 'red':
                    red_wins += 1
                elif result['winner'] == 'blue':
                    blue_wins += 1
                else:
                    ties += 1
                
                if (i + 1) % 20 == 0:
                    print(f"  进度: {i + 1}/{num_games}")
            
            avg_red_score = total_red_score / num_games
            avg_blue_score = total_blue_score / num_games
            
            results[f"{red_name} vs {blue_name}"] = {
                'red_wins': red_wins,
                'blue_wins': blue_wins,
                'ties': ties,
                'avg_red_score': avg_red_score,
                'avg_blue_score': avg_blue_score
            }
            
            print(f"  结果: 红{red_wins} - 蓝{blue_wins} - 平{ties}")
            print(f"  平均分数: 红{avg_red_score:.1f} - 蓝{avg_blue_score:.1f}")
            print()
    
    # 输出总结
    print("📈 基准测试总结:")
    print("-" * 50)
    for matchup, stats in results.items():
        print(f"{matchup}:")
        print(f"  胜率: 红{stats['red_wins']/num_games*100:.1f}% - 蓝{stats['blue_wins']/num_games*100:.1f}%")
        print(f"  平均分: 红{stats['avg_red_score']:.1f} - 蓝{stats['avg_blue_score']:.1f}")
        print()


def run_single_game_analysis():
    """运行单局游戏分析"""
    print("🔍 单局游戏分析")
    print("=" * 50)
    
    game = LatticeMessengersGame()
    red_agent = HeuristicAgent(Team.RED)
    blue_agent = GreedyAgent(Team.BLUE)
    game.set_ai_agents(red_agent, blue_agent)
    
    result = game.run_game(verbose=True)
    
    print("\n📋 详细分析:")
    print(f"游戏时长: {result['total_turns']} 回合")
    
    # 分析每个信使的表现
    print("\n信使表现分析:")
    for messenger in game.get_all_messengers():
        team_name = "红队" if messenger.team == Team.RED else "蓝队"
        print(f"{team_name}信使{messenger.messenger_id}:")
        print(f"  最终位置: ({messenger.x}, {messenger.y})")
        print(f"  总移动次数: {messenger.total_moves}")
        print(f"  总咏唱次数: {messenger.total_chants}")
        print(f"  剩余咏唱能量: {messenger.current_chant_energy}")
        print(f"  惩罚分数: {messenger.penalty_score:.1f}")
    
    # 分析最终环境状态
    print(f"\n环境分析:")
    red_resonance_count = 0
    blue_resonance_count = 0
    
    for x in range(game.environment.grid_size):
        for y in range(game.environment.grid_size):
            energy = game.environment.get_energy(x, y)
            if energy > 5:
                red_resonance_count += 1
            elif energy < -5:
                blue_resonance_count += 1
    
    print(f"红色共鸣点数量: {red_resonance_count}")
    print(f"蓝色共鸣点数量: {blue_resonance_count}")
    print(f"红队最长链长度: {int(result['red_score'] ** 0.5) if result['red_score'] > 0 else 0}")
    print(f"蓝队最长链长度: {int(result['blue_score'] ** 0.5) if result['blue_score'] > 0 else 0}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="晶格信使游戏")
    parser.add_argument('mode', choices=['console', 'visual', 'benchmark', 'analysis'], 
                       help='运行模式')
    
    args = parser.parse_args()
    
    if args.mode == 'console':
        run_console_game()
    elif args.mode == 'visual':
        run_visual_game()
    elif args.mode == 'benchmark':
        run_benchmark()
    elif args.mode == 'analysis':
        run_single_game_analysis()


if __name__ == "__main__":
    if len(sys.argv) == 1:
        # 如果没有参数，显示菜单
        print("🎮 晶格信使 - The Lattice Messengers")
        print("=" * 50)
        print("请选择运行模式:")
        print("1. 控制台版本 (console)")
        print("2. 可视化版本 (visual)")
        print("3. 性能基准测试 (benchmark)")
        print("4. 单局分析 (analysis)")
        print()
        print("使用方法: python main.py [mode]")
        print("例如: python main.py visual")
    else:
        main()
