"""
游戏环境模块 - 管理10x10网格世界和能量晶格
"""

import numpy as np
from typing import List, Tuple, Set
from enum import Enum


class Team(Enum):
    """团队枚举"""
    RED = "red"
    BLUE = "blue"


class GameEnvironment:
    """游戏环境类 - 管理网格世界和能量晶格"""
    
    def __init__(self, grid_size: int = 10):
        """
        初始化游戏环境
        
        Args:
            grid_size: 网格大小，默认10x10
        """
        self.grid_size = grid_size
        self.energy_grid = np.zeros((grid_size, grid_size), dtype=int)
        self.min_energy = -10
        self.max_energy = 10
        
    def is_valid_position(self, x: int, y: int) -> bool:
        """检查位置是否有效"""
        return 0 <= x < self.grid_size and 0 <= y < self.grid_size
    
    def get_energy(self, x: int, y: int) -> int:
        """获取指定位置的能量值"""
        if not self.is_valid_position(x, y):
            raise ValueError(f"位置 ({x}, {y}) 超出网格范围")
        return self.energy_grid[x, y]
    
    def set_energy(self, x: int, y: int, energy: int) -> None:
        """设置指定位置的能量值"""
        if not self.is_valid_position(x, y):
            raise ValueError(f"位置 ({x}, {y}) 超出网格范围")
        
        # 限制能量值在有效范围内
        energy = max(self.min_energy, min(self.max_energy, energy))
        self.energy_grid[x, y] = energy
    
    def modify_energy(self, x: int, y: int, delta: int) -> int:
        """
        修改指定位置的能量值
        
        Args:
            x, y: 位置坐标
            delta: 能量变化量
            
        Returns:
            修改后的能量值
        """
        current_energy = self.get_energy(x, y)
        new_energy = current_energy + delta
        self.set_energy(x, y, new_energy)
        return self.get_energy(x, y)
    
    def get_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]:
        """获取指定位置的相邻位置（上下左右）"""
        neighbors = []
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # 上下左右
        
        for dx, dy in directions:
            nx, ny = x + dx, y + dy
            if self.is_valid_position(nx, ny):
                neighbors.append((nx, ny))
        
        return neighbors
    
    def is_red_resonance(self, x: int, y: int) -> bool:
        """检查是否为红色共鸣（能量值 > +5）"""
        return self.get_energy(x, y) > 5
    
    def is_blue_resonance(self, x: int, y: int) -> bool:
        """检查是否为蓝色共鸣（能量值 < -5）"""
        return self.get_energy(x, y) < -5
    
    def find_resonance_chain(self, start_x: int, start_y: int, team: Team) -> Set[Tuple[int, int]]:
        """
        使用深度优先搜索找到从指定位置开始的共鸣长链
        
        Args:
            start_x, start_y: 起始位置
            team: 团队类型
            
        Returns:
            共鸣长链中所有位置的集合
        """
        if team == Team.RED and not self.is_red_resonance(start_x, start_y):
            return set()
        elif team == Team.BLUE and not self.is_blue_resonance(start_x, start_y):
            return set()
        
        visited = set()
        chain = set()
        
        def dfs(x: int, y: int):
            if (x, y) in visited:
                return
            
            visited.add((x, y))
            
            # 检查当前位置是否符合共鸣条件
            is_resonance = (team == Team.RED and self.is_red_resonance(x, y)) or \
                          (team == Team.BLUE and self.is_blue_resonance(x, y))
            
            if is_resonance:
                chain.add((x, y))
                # 继续搜索相邻位置
                for nx, ny in self.get_neighbors(x, y):
                    dfs(nx, ny)
        
        dfs(start_x, start_y)
        return chain
    
    def get_longest_resonance_chain(self, team: Team) -> int:
        """
        获取指定团队的最长共鸣长链长度
        
        Args:
            team: 团队类型
            
        Returns:
            最长共鸣长链的长度
        """
        visited_global = set()
        max_chain_length = 0
        
        for x in range(self.grid_size):
            for y in range(self.grid_size):
                if (x, y) not in visited_global:
                    # 检查是否为对应团队的共鸣点
                    is_resonance = (team == Team.RED and self.is_red_resonance(x, y)) or \
                                  (team == Team.BLUE and self.is_blue_resonance(x, y))
                    
                    if is_resonance:
                        chain = self.find_resonance_chain(x, y, team)
                        visited_global.update(chain)
                        max_chain_length = max(max_chain_length, len(chain))
        
        return max_chain_length
    
    def get_team_score(self, team: Team) -> int:
        """
        计算团队得分（最长共鸣长链长度的平方）
        
        Args:
            team: 团队类型
            
        Returns:
            团队得分
        """
        longest_chain = self.get_longest_resonance_chain(team)
        return longest_chain ** 2
    
    def get_energy_color_value(self, energy: int) -> float:
        """
        将能量值转换为颜色值（0-1范围）
        用于可视化显示
        
        Args:
            energy: 能量值
            
        Returns:
            颜色值（0为深蓝，0.5为中性灰，1为亮红）
        """
        # 将能量值从[-10, 10]映射到[0, 1]
        normalized = (energy - self.min_energy) / (self.max_energy - self.min_energy)
        return max(0.0, min(1.0, normalized))
    
    def reset(self) -> None:
        """重置环境到初始状态"""
        self.energy_grid = np.zeros((self.grid_size, self.grid_size), dtype=int)
    
    def get_state_copy(self) -> np.ndarray:
        """获取当前环境状态的副本"""
        return self.energy_grid.copy()
    
    def __str__(self) -> str:
        """返回环境的字符串表示"""
        result = "游戏环境状态:\n"
        for row in self.energy_grid:
            result += " ".join(f"{energy:3d}" for energy in row) + "\n"
        return result
