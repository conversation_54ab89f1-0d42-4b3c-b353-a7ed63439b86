"""
基础使用示例
演示如何使用晶格信使游戏框架
"""

from lattice_messengers import (
    LatticeMessengersGame,
    RandomAgent,
    GreedyAgent, 
    HeuristicAgent,
    Team
)


def example_1_basic_game():
    """示例1: 基础游戏运行"""
    print("示例1: 基础游戏运行")
    print("-" * 30)
    
    # 创建游戏实例
    game = LatticeMessengersGame()
    
    # 设置AI代理
    red_agent = RandomAgent(Team.RED)
    blue_agent = RandomAgent(Team.BLUE)
    game.set_ai_agents(red_agent, blue_agent)
    
    # 运行游戏
    result = game.run_game(verbose=True)
    
    print(f"游戏结果: {result}")
    print()


def example_2_different_agents():
    """示例2: 不同AI代理对战"""
    print("示例2: 不同AI代理对战")
    print("-" * 30)
    
    game = LatticeMessengersGame()
    
    # 启发式代理 vs 贪心代理
    red_agent = HeuristicAgent(Team.RED)
    blue_agent = GreedyAgent(Team.BLUE)
    game.set_ai_agents(red_agent, blue_agent)
    
    result = game.run_game(verbose=True)
    
    print(f"启发式代理 vs 贪心代理结果:")
    print(f"红队(启发式): {result['red_score']}")
    print(f"蓝队(贪心): {result['blue_score']}")
    print(f"获胜者: {result['winner']}")
    print()


def example_3_custom_settings():
    """示例3: 自定义游戏设置"""
    print("示例3: 自定义游戏设置")
    print("-" * 30)
    
    # 创建自定义设置的游戏
    game = LatticeMessengersGame(
        grid_size=8,           # 8x8网格
        messengers_per_team=2, # 每队2个信使
        max_turns=20          # 20回合
    )
    
    red_agent = HeuristicAgent(Team.RED)
    blue_agent = HeuristicAgent(Team.BLUE)
    game.set_ai_agents(red_agent, blue_agent)
    
    result = game.run_game(verbose=True)
    
    print(f"自定义设置游戏结果: {result}")
    print()


def example_4_manual_control():
    """示例4: 手动控制游戏流程"""
    print("示例4: 手动控制游戏流程")
    print("-" * 30)
    
    game = LatticeMessengersGame()
    red_agent = GreedyAgent(Team.RED)
    blue_agent = GreedyAgent(Team.BLUE)
    game.set_ai_agents(red_agent, blue_agent)
    
    # 手动执行回合
    for turn in range(5):  # 只运行5回合
        print(f"执行第 {turn + 1} 回合...")
        
        # 显示当前状态
        print(f"当前分数 - 红队: {game.state.red_score}, 蓝队: {game.state.blue_score}")
        
        # 执行一回合
        continue_game = game.execute_turn()
        
        if not continue_game:
            print("游戏结束!")
            break
    
    print(f"最终分数 - 红队: {game.state.red_score}, 蓝队: {game.state.blue_score}")
    print()


def example_5_environment_analysis():
    """示例5: 环境状态分析"""
    print("示例5: 环境状态分析")
    print("-" * 30)
    
    game = LatticeMessengersGame()
    
    # 手动修改环境进行测试
    env = game.environment
    
    # 创建一些能量模式
    env.set_energy(2, 2, 8)   # 红色共鸣
    env.set_energy(2, 3, 7)   # 红色共鸣
    env.set_energy(2, 4, 6)   # 红色共鸣
    
    env.set_energy(7, 7, -8)  # 蓝色共鸣
    env.set_energy(7, 8, -7)  # 蓝色共鸣
    env.set_energy(8, 7, -6)  # 蓝色共鸣
    
    print("环境状态:")
    print(env)
    
    # 分析共鸣链
    red_chain_length = env.get_longest_resonance_chain(Team.RED)
    blue_chain_length = env.get_longest_resonance_chain(Team.BLUE)
    
    print(f"红队最长共鸣链: {red_chain_length}")
    print(f"蓝队最长共鸣链: {blue_chain_length}")
    
    print(f"红队得分: {env.get_team_score(Team.RED)}")
    print(f"蓝队得分: {env.get_team_score(Team.BLUE)}")
    print()


def example_6_messenger_actions():
    """示例6: 信使行动演示"""
    print("示例6: 信使行动演示")
    print("-" * 30)
    
    from lattice_messengers.messenger import Action, ActionType
    
    game = LatticeMessengersGame()
    messenger = game.red_messengers[0]
    env = game.environment
    
    print(f"信使初始状态: {messenger}")
    print(f"初始位置能量: {env.get_energy(messenger.x, messenger.y)}")
    
    # 演示移动
    move_action = Action(ActionType.MOVE, (1, 0))  # 向右移动
    success = messenger.execute_action(env, move_action)
    print(f"移动结果: {success}, 新位置: {messenger.position}")
    
    # 演示咏唱
    chant_action = Action(ActionType.CHANT)
    success = messenger.execute_action(env, chant_action)
    print(f"咏唱结果: {success}, 位置能量: {env.get_energy(messenger.x, messenger.y)}")
    print(f"剩余咏唱能量: {messenger.current_chant_energy}")
    
    # 获取可用行动
    available_actions = messenger.get_available_actions(env)
    print(f"可用行动数量: {len(available_actions)}")
    print()


if __name__ == "__main__":
    print("🎮 晶格信使 - 基础使用示例")
    print("=" * 50)
    
    example_1_basic_game()
    example_2_different_agents()
    example_3_custom_settings()
    example_4_manual_control()
    example_5_environment_analysis()
    example_6_messenger_actions()
    
    print("✅ 所有示例运行完成!")
