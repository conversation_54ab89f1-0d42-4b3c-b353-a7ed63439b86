# 晶格信使 (The Lattice Messengers)

一个基于Python的AI进化模拟游戏，两个团队的信使通过咏唱改造世界，争夺最长的共鸣长链。

## 🎮 游戏概述

### 游戏世界
- 10x10的网格世界，每个单元格是一个能量晶格
- 能量值范围：-10（深蓝色）到 +10（亮红色）
- 初始状态：所有晶格能量值为0（中性灰色）

### 游戏角色
- **红队**：3个信使，咏唱时能量+1
- **蓝队**：3个信使，咏唱时能量-1
- 每个信使有有限的咏唱能量（默认20点）

### 行动机制
- **移动（Move）**：上下左右移动到相邻格子
- **咏唱（Chant）**：改变当前晶格的能量值
- **等待（Wait）**：跳过当前回合

### 胜利条件
- 游戏持续30回合
- **共鸣定义**：红色共鸣（能量>+5），蓝色共鸣（能量<-5）
- **共鸣长链**：由同色共鸣晶格组成的连续路径
- **得分计算**：最长共鸣长链长度的平方
- **获胜条件**：得分高的团队获胜

## 🚀 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 运行游戏

#### 1. 可视化版本（推荐）
```bash
python main.py visual
```

#### 2. 控制台版本
```bash
python main.py console
```

#### 3. 性能基准测试
```bash
python main.py benchmark
```

#### 4. 单局分析
```bash
python main.py analysis
```

### 基础使用示例
```python
from lattice_messengers import LatticeMessengersGame, HeuristicAgent, GreedyAgent, Team

# 创建游戏
game = LatticeMessengersGame()

# 设置AI代理
red_agent = HeuristicAgent(Team.RED)
blue_agent = GreedyAgent(Team.BLUE)
game.set_ai_agents(red_agent, blue_agent)

# 运行游戏
result = game.run_game(verbose=True)
print(f"获胜者: {result['winner']}")
```

## 🏗️ 项目结构

```
lattice_messengers/
├── __init__.py          # 包初始化
├── environment.py       # 游戏环境和网格管理
├── messenger.py         # 信使角色和行动机制
├── game.py             # 游戏主控制器
├── ai_agent.py         # AI智能体框架
└── visualizer.py       # 可视化界面

examples/
└── basic_usage.py      # 基础使用示例

main.py                 # 主程序入口
requirements.txt        # 依赖包列表
README.md              # 项目说明
```

## 🤖 AI代理类型

### 1. RandomAgent（随机代理）
- 随机选择可用行动
- 适合作为基准对比

### 2. GreedyAgent（贪心代理）
- 优先在有利位置咏唱
- 寻找最佳移动位置

### 3. HeuristicAgent（启发式代理）
- 结合探索和利用策略
- 考虑游戏阶段和周围环境
- 更智能的决策机制

## 🎯 自定义AI代理

继承`AIAgent`基类创建自定义代理：

```python
from lattice_messengers import AIAgent, Action, ActionType

class MyCustomAgent(AIAgent):
    def get_actions(self, game_state):
        actions = []
        team_messengers = game_state['team_messengers']
        
        for messenger_state in team_messengers:
            # 实现你的策略逻辑
            action = self.choose_action(messenger_state, game_state)
            actions.append(action)
        
        return actions
    
    def choose_action(self, messenger_state, game_state):
        # 你的决策逻辑
        return Action(ActionType.WAIT)
```

## 🎮 可视化界面控制

- **空格键**：执行下一回合
- **A键**：开启/关闭自动播放
- **R键**：重置游戏
- **ESC键**：退出游戏

## 📊 游戏分析功能

### 性能基准测试
比较不同AI代理的表现：
```bash
python main.py benchmark
```

### 单局详细分析
分析单局游戏的详细数据：
```bash
python main.py analysis
```

## 🔧 高级功能

### 自定义游戏设置
```python
game = LatticeMessengersGame(
    grid_size=8,           # 网格大小
    messengers_per_team=2, # 每队信使数量
    max_turns=20          # 最大回合数
)
```

### 手动控制游戏流程
```python
# 逐回合执行
while game.execute_turn():
    print(f"回合 {game.state.current_turn}")
    print(f"分数: 红{game.state.red_score} - 蓝{game.state.blue_score}")
```

### 环境状态分析
```python
env = game.environment

# 获取共鸣链长度
red_chain = env.get_longest_resonance_chain(Team.RED)
blue_chain = env.get_longest_resonance_chain(Team.BLUE)

# 计算团队得分
red_score = env.get_team_score(Team.RED)
blue_score = env.get_team_score(Team.BLUE)
```

## 🧠 强化学习支持

框架设计支持强化学习训练：

1. **状态空间**：网格能量分布 + 信使位置和状态
2. **行动空间**：移动、咏唱、等待
3. **奖励机制**：最终得分 - 行动惩罚
4. **环境接口**：符合OpenAI Gym标准

## 📈 扩展建议

1. **深度强化学习**：使用DQN、PPO等算法训练智能体
2. **多智能体学习**：实现MADDPG等多智能体算法
3. **进化算法**：使用遗传算法优化策略
4. **神经网络**：设计卷积神经网络处理网格状态
5. **自对弈训练**：实现AlphaZero风格的自对弈学习

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

---

**搞完了！** 🎉

现在你有了一个完整的"晶格信使"AI进化模拟游戏框架，包含：
- 完整的游戏逻辑和规则实现
- 多种AI代理类型
- 可视化界面
- 性能分析工具
- 扩展性强的架构设计

快来体验这个独特的AI对战游戏吧！
