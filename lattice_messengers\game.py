"""
游戏主控制器模块 - 管理游戏流程和规则
"""

import random
from typing import List, Dict, Optional, Tuple
from .environment import GameEnvironment, Team
from .messenger import Messenger, Action
from .ai_agent import AIAgent


class GameState:
    """游戏状态类"""
    
    def __init__(self):
        self.current_turn = 0
        self.max_turns = 30
        self.game_over = False
        self.winner = None
        self.red_score = 0
        self.blue_score = 0


class LatticeMessengersGame:
    """晶格信使游戏主控制器"""
    
    def __init__(self, grid_size: int = 10, messengers_per_team: int = 3, max_turns: int = 30):
        """
        初始化游戏
        
        Args:
            grid_size: 网格大小
            messengers_per_team: 每队信使数量
            max_turns: 最大回合数
        """
        self.environment = GameEnvironment(grid_size)
        self.messengers_per_team = messengers_per_team
        self.state = GameState()
        self.state.max_turns = max_turns
        
        # 初始化信使
        self.red_messengers: List[Messenger] = []
        self.blue_messengers: List[Messenger] = []
        self._initialize_messengers()
        
        # 游戏日志
        self.game_log: List[Dict] = []
        
        # AI代理
        self.red_agent: Optional[AIAgent] = None
        self.blue_agent: Optional[AIAgent] = None
    
    def _initialize_messengers(self) -> None:
        """初始化信使位置"""
        grid_size = self.environment.grid_size
        
        # 红队信使初始位置（左侧）
        red_positions = [
            (0, 1), (0, 4), (0, 7)
        ]
        
        # 蓝队信使初始位置（右侧）
        blue_positions = [
            (grid_size-1, 2), (grid_size-1, 5), (grid_size-1, 8)
        ]
        
        # 创建红队信使
        for i, (x, y) in enumerate(red_positions[:self.messengers_per_team]):
            messenger = Messenger(i, Team.RED, x, y)
            self.red_messengers.append(messenger)
        
        # 创建蓝队信使
        for i, (x, y) in enumerate(blue_positions[:self.messengers_per_team]):
            messenger = Messenger(i, Team.BLUE, x, y)
            self.blue_messengers.append(messenger)
    
    def set_ai_agents(self, red_agent: Optional[AIAgent], blue_agent: Optional[AIAgent]) -> None:
        """设置AI代理"""
        self.red_agent = red_agent
        self.blue_agent = blue_agent
    
    def get_all_messengers(self) -> List[Messenger]:
        """获取所有信使"""
        return self.red_messengers + self.blue_messengers
    
    def get_team_messengers(self, team: Team) -> List[Messenger]:
        """获取指定团队的信使"""
        if team == Team.RED:
            return self.red_messengers
        else:
            return self.blue_messengers
    
    def is_position_occupied(self, x: int, y: int, exclude_messenger: Optional[Messenger] = None) -> bool:
        """检查位置是否被其他信使占据"""
        for messenger in self.get_all_messengers():
            if messenger != exclude_messenger and messenger.x == x and messenger.y == y:
                return True
        return False
    
    def get_game_state_for_ai(self, team: Team) -> Dict:
        """
        获取AI可观察的游戏状态
        
        Args:
            team: 请求状态的团队
            
        Returns:
            游戏状态字典
        """
        return {
            'environment': self.environment.get_state_copy(),
            'current_turn': self.state.current_turn,
            'max_turns': self.state.max_turns,
            'team_messengers': [m.get_state_dict() for m in self.get_team_messengers(team)],
            'enemy_messengers': [m.get_state_dict() for m in self.get_team_messengers(
                Team.BLUE if team == Team.RED else Team.RED
            )],
            'red_score': self.state.red_score,
            'blue_score': self.state.blue_score,
            'game_over': self.state.game_over
        }
    
    def execute_turn(self) -> bool:
        """
        执行一个回合
        
        Returns:
            游戏是否继续
        """
        if self.state.game_over:
            return False
        
        turn_log = {
            'turn': self.state.current_turn,
            'actions': [],
            'scores': {}
        }
        
        # 获取所有信使的行动
        all_actions = []
        
        # 红队行动
        if self.red_agent:
            game_state = self.get_game_state_for_ai(Team.RED)
            red_actions = self.red_agent.get_actions(game_state)
        else:
            red_actions = [self._get_random_action(m) for m in self.red_messengers]
        
        # 蓝队行动
        if self.blue_agent:
            game_state = self.get_game_state_for_ai(Team.BLUE)
            blue_actions = self.blue_agent.get_actions(game_state)
        else:
            blue_actions = [self._get_random_action(m) for m in self.blue_messengers]
        
        # 合并行动
        for i, action in enumerate(red_actions):
            all_actions.append((self.red_messengers[i], action))
        
        for i, action in enumerate(blue_actions):
            all_actions.append((self.blue_messengers[i], action))
        
        # 随机打乱执行顺序
        random.shuffle(all_actions)
        
        # 执行所有行动
        for messenger, action in all_actions:
            success = messenger.execute_action(self.environment, action)
            
            action_log = {
                'messenger_id': messenger.messenger_id,
                'team': messenger.team.value,
                'action': str(action),
                'success': success,
                'position': messenger.position
            }
            turn_log['actions'].append(action_log)
        
        # 更新分数
        self.state.red_score = self.environment.get_team_score(Team.RED)
        self.state.blue_score = self.environment.get_team_score(Team.BLUE)
        
        turn_log['scores'] = {
            'red': self.state.red_score,
            'blue': self.state.blue_score
        }
        
        self.game_log.append(turn_log)
        
        # 检查游戏是否结束
        self.state.current_turn += 1
        if self.state.current_turn >= self.state.max_turns:
            self._end_game()
        
        return not self.state.game_over
    
    def _get_random_action(self, messenger: Messenger) -> Action:
        """为信使生成随机行动（用于测试）"""
        available_actions = messenger.get_available_actions(self.environment)
        return random.choice(available_actions)
    
    def _end_game(self) -> None:
        """结束游戏并确定获胜者"""
        self.state.game_over = True
        
        if self.state.red_score > self.state.blue_score:
            self.state.winner = Team.RED
        elif self.state.blue_score > self.state.red_score:
            self.state.winner = Team.BLUE
        else:
            self.state.winner = None  # 平局
        
        # 通知AI代理游戏结束
        if self.red_agent:
            self.red_agent.on_game_end(self.state.red_score, self.state.winner == Team.RED)
        
        if self.blue_agent:
            self.blue_agent.on_game_end(self.state.blue_score, self.state.winner == Team.BLUE)
    
    def reset(self) -> None:
        """重置游戏到初始状态"""
        self.environment.reset()
        self.state = GameState()
        self.state.max_turns = 30
        self.game_log.clear()
        
        # 重置信使
        self._initialize_messengers()
    
    def run_game(self, verbose: bool = False) -> Dict:
        """
        运行完整游戏
        
        Args:
            verbose: 是否输出详细信息
            
        Returns:
            游戏结果字典
        """
        if verbose:
            print("🎮 晶格信使游戏开始！")
            print(f"网格大小: {self.environment.grid_size}x{self.environment.grid_size}")
            print(f"每队信使数量: {self.messengers_per_team}")
            print(f"最大回合数: {self.state.max_turns}")
            print("-" * 50)
        
        while self.execute_turn():
            if verbose and self.state.current_turn % 5 == 0:
                print(f"回合 {self.state.current_turn}: 红队 {self.state.red_score} - {self.state.blue_score} 蓝队")
        
        # 游戏结果
        result = {
            'winner': self.state.winner.value if self.state.winner else 'tie',
            'red_score': self.state.red_score,
            'blue_score': self.state.blue_score,
            'total_turns': self.state.current_turn,
            'game_log': self.game_log
        }
        
        if verbose:
            print("-" * 50)
            print("🏆 游戏结束！")
            print(f"红队得分: {self.state.red_score}")
            print(f"蓝队得分: {self.state.blue_score}")
            if self.state.winner:
                winner_name = "红队" if self.state.winner == Team.RED else "蓝队"
                print(f"获胜者: {winner_name}")
            else:
                print("结果: 平局")
        
        return result
