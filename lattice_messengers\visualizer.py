"""
可视化模块 - 使用pygame实现游戏的图形界面
"""

import pygame
import numpy as np
from typing import List, Tuple, Optional
from .game import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .environment import Team
from .messenger import Messenger


class GameVisualizer:
    """游戏可视化器"""
    
    def __init__(self, game: <PERSON><PERSON><PERSON>MessengersGame, cell_size: int = 60, window_width: int = 800):
        """
        初始化可视化器
        
        Args:
            game: 游戏实例
            cell_size: 每个网格单元的像素大小
            window_width: 窗口宽度
        """
        self.game = game
        self.cell_size = cell_size
        self.grid_size = game.environment.grid_size
        
        # 计算窗口尺寸
        self.grid_width = self.grid_size * cell_size
        self.grid_height = self.grid_size * cell_size
        self.info_panel_width = window_width - self.grid_width
        self.window_width = window_width
        self.window_height = max(self.grid_height, 600)
        
        # 初始化pygame
        pygame.init()
        self.screen = pygame.display.set_mode((self.window_width, self.window_height))
        pygame.display.set_caption("晶格信使 - The Lattice Messengers")
        
        # 字体
        self.font_large = pygame.font.Font(None, 36)
        self.font_medium = pygame.font.Font(None, 24)
        self.font_small = pygame.font.Font(None, 18)
        
        # 颜色定义
        self.colors = {
            'background': (240, 240, 240),
            'grid_line': (200, 200, 200),
            'text': (50, 50, 50),
            'red_team': (220, 50, 50),
            'blue_team': (50, 50, 220),
            'neutral': (128, 128, 128),
            'panel_bg': (250, 250, 250),
            'panel_border': (180, 180, 180)
        }
        
        self.running = True
        self.clock = pygame.time.Clock()
        self.auto_play = False
        self.auto_play_speed = 2  # 每秒回合数
    
    def get_energy_color(self, energy: int) -> Tuple[int, int, int]:
        """
        根据能量值获取颜色
        
        Args:
            energy: 能量值 (-10 到 +10)
            
        Returns:
            RGB颜色元组
        """
        # 将能量值映射到0-1范围
        normalized = (energy + 10) / 20.0
        normalized = max(0.0, min(1.0, normalized))
        
        if normalized < 0.5:
            # 蓝色到灰色 (能量值 -10 到 0)
            blue_intensity = int(255 * (1 - normalized * 2))
            gray_intensity = int(128 * normalized * 2)
            return (gray_intensity, gray_intensity, 128 + blue_intensity // 2)
        else:
            # 灰色到红色 (能量值 0 到 +10)
            red_intensity = int(255 * (normalized - 0.5) * 2)
            gray_intensity = int(128 * (1 - (normalized - 0.5) * 2))
            return (128 + red_intensity // 2, gray_intensity, gray_intensity)
    
    def draw_grid(self) -> None:
        """绘制网格和能量晶格"""
        environment = self.game.environment
        
        for x in range(self.grid_size):
            for y in range(self.grid_size):
                # 计算屏幕坐标
                screen_x = x * self.cell_size
                screen_y = y * self.cell_size
                
                # 获取能量值和对应颜色
                energy = environment.get_energy(x, y)
                color = self.get_energy_color(energy)
                
                # 绘制单元格
                rect = pygame.Rect(screen_x, screen_y, self.cell_size, self.cell_size)
                pygame.draw.rect(self.screen, color, rect)
                pygame.draw.rect(self.screen, self.colors['grid_line'], rect, 1)
                
                # 绘制能量值文本
                if energy != 0:
                    text_color = (255, 255, 255) if abs(energy) > 5 else (0, 0, 0)
                    text = self.font_small.render(str(energy), True, text_color)
                    text_rect = text.get_rect(center=(screen_x + self.cell_size // 2, 
                                                    screen_y + self.cell_size // 2))
                    self.screen.blit(text, text_rect)
    
    def draw_messengers(self) -> None:
        """绘制信使"""
        all_messengers = self.game.get_all_messengers()
        
        for messenger in all_messengers:
            # 计算屏幕坐标
            screen_x = messenger.x * self.cell_size + self.cell_size // 2
            screen_y = messenger.y * self.cell_size + self.cell_size // 2
            
            # 选择颜色
            color = self.colors['red_team'] if messenger.team == Team.RED else self.colors['blue_team']
            
            # 绘制信使圆圈
            radius = self.cell_size // 4
            pygame.draw.circle(self.screen, color, (screen_x, screen_y), radius)
            pygame.draw.circle(self.screen, (255, 255, 255), (screen_x, screen_y), radius, 2)
            
            # 绘制信使ID
            text = self.font_small.render(str(messenger.messenger_id), True, (255, 255, 255))
            text_rect = text.get_rect(center=(screen_x, screen_y))
            self.screen.blit(text, text_rect)
            
            # 绘制咏唱能量条
            if messenger.current_chant_energy < messenger.max_chant_energy:
                bar_width = self.cell_size - 10
                bar_height = 4
                bar_x = messenger.x * self.cell_size + 5
                bar_y = messenger.y * self.cell_size + self.cell_size - 8
                
                # 背景条
                pygame.draw.rect(self.screen, (100, 100, 100), 
                               (bar_x, bar_y, bar_width, bar_height))
                
                # 能量条
                energy_ratio = messenger.current_chant_energy / messenger.max_chant_energy
                energy_width = int(bar_width * energy_ratio)
                pygame.draw.rect(self.screen, color, 
                               (bar_x, bar_y, energy_width, bar_height))
    
    def draw_info_panel(self) -> None:
        """绘制信息面板"""
        panel_x = self.grid_width
        panel_y = 0
        panel_width = self.info_panel_width
        panel_height = self.window_height
        
        # 绘制面板背景
        pygame.draw.rect(self.screen, self.colors['panel_bg'], 
                        (panel_x, panel_y, panel_width, panel_height))
        pygame.draw.line(self.screen, self.colors['panel_border'], 
                        (panel_x, panel_y), (panel_x, panel_y + panel_height), 2)
        
        y_offset = 20
        
        # 游戏标题
        title = self.font_large.render("晶格信使", True, self.colors['text'])
        self.screen.blit(title, (panel_x + 10, y_offset))
        y_offset += 50
        
        # 回合信息
        turn_text = f"回合: {self.game.state.current_turn}/{self.game.state.max_turns}"
        turn_surface = self.font_medium.render(turn_text, True, self.colors['text'])
        self.screen.blit(turn_surface, (panel_x + 10, y_offset))
        y_offset += 30
        
        # 分数信息
        red_score_text = f"🔴 红队: {self.game.state.red_score}"
        blue_score_text = f"🔵 蓝队: {self.game.state.blue_score}"
        
        red_surface = self.font_medium.render(red_score_text, True, self.colors['red_team'])
        blue_surface = self.font_medium.render(blue_score_text, True, self.colors['blue_team'])
        
        self.screen.blit(red_surface, (panel_x + 10, y_offset))
        y_offset += 25
        self.screen.blit(blue_surface, (panel_x + 10, y_offset))
        y_offset += 40
        
        # 信使状态
        status_title = self.font_medium.render("信使状态:", True, self.colors['text'])
        self.screen.blit(status_title, (panel_x + 10, y_offset))
        y_offset += 30
        
        # 红队信使
        for messenger in self.game.red_messengers:
            text = f"🔴{messenger.messenger_id}: ({messenger.x},{messenger.y}) E:{messenger.current_chant_energy}"
            surface = self.font_small.render(text, True, self.colors['text'])
            self.screen.blit(surface, (panel_x + 10, y_offset))
            y_offset += 20
        
        y_offset += 10
        
        # 蓝队信使
        for messenger in self.game.blue_messengers:
            text = f"🔵{messenger.messenger_id}: ({messenger.x},{messenger.y}) E:{messenger.current_chant_energy}"
            surface = self.font_small.render(text, True, self.colors['text'])
            self.screen.blit(surface, (panel_x + 10, y_offset))
            y_offset += 20
        
        y_offset += 30
        
        # 控制说明
        controls = [
            "控制:",
            "空格键 - 下一回合",
            "A - 自动播放",
            "R - 重置游戏",
            "ESC - 退出"
        ]
        
        for control in controls:
            surface = self.font_small.render(control, True, self.colors['text'])
            self.screen.blit(surface, (panel_x + 10, y_offset))
            y_offset += 18
        
        # 游戏状态
        if self.game.state.game_over:
            y_offset += 20
            if self.game.state.winner:
                winner_text = "🔴 红队获胜!" if self.game.state.winner == Team.RED else "🔵 蓝队获胜!"
                color = self.colors['red_team'] if self.game.state.winner == Team.RED else self.colors['blue_team']
            else:
                winner_text = "平局!"
                color = self.colors['text']
            
            winner_surface = self.font_medium.render(winner_text, True, color)
            self.screen.blit(winner_surface, (panel_x + 10, y_offset))
    
    def handle_events(self) -> None:
        """处理事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            elif event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    self.running = False
                elif event.key == pygame.K_SPACE and not self.game.state.game_over:
                    self.game.execute_turn()
                elif event.key == pygame.K_r:
                    self.game.reset()
                elif event.key == pygame.K_a:
                    self.auto_play = not self.auto_play
    
    def run(self) -> None:
        """运行可视化界面"""
        last_auto_play_time = 0
        
        while self.running:
            current_time = pygame.time.get_ticks()
            
            self.handle_events()
            
            # 自动播放逻辑
            if self.auto_play and not self.game.state.game_over:
                if current_time - last_auto_play_time > (1000 // self.auto_play_speed):
                    self.game.execute_turn()
                    last_auto_play_time = current_time
            
            # 绘制
            self.screen.fill(self.colors['background'])
            self.draw_grid()
            self.draw_messengers()
            self.draw_info_panel()
            
            pygame.display.flip()
            self.clock.tick(60)
        
        pygame.quit()


def create_demo_game() -> LatticeMessengersGame:
    """创建演示游戏"""
    from .ai_agent import RandomAgent, GreedyAgent, HeuristicAgent
    
    game = LatticeMessengersGame()
    
    # 设置AI代理
    red_agent = HeuristicAgent(Team.RED)
    blue_agent = GreedyAgent(Team.BLUE)
    
    game.set_ai_agents(red_agent, blue_agent)
    
    return game
