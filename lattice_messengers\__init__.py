"""
晶格信使 (The Lattice Messengers) - AI进化模拟游戏

一个基于网格的策略游戏，两个团队的信使通过咏唱改造世界，
争夺最长的共鸣长链。
"""

__version__ = "1.0.0"
__author__ = "AI Assistant"

from .game import <PERSON><PERSON>ceMessengersGame
from .environment import GameEnvironment
from .messenger import Messenger, Team
from .ai_agent import AIAgent, RandomAgent
from .visualizer import GameVisualizer

__all__ = [
    'LatticeMessengersGame',
    'GameEnvironment', 
    'Messenger',
    'Team',
    'AIAgent',
    'RandomAgent',
    'GameVisualizer'
]
