"""
AI智能体模块 - 提供AI代理的基础框架
"""

import random
import numpy as np
from abc import ABC, abstractmethod
from typing import List, Dict, Any
from .messenger import Action, ActionType
from .environment import Team


class AIAgent(ABC):
    """AI代理基类"""
    
    def __init__(self, team: Team, agent_name: str = "AIAgent"):
        """
        初始化AI代理
        
        Args:
            team: 所属团队
            agent_name: 代理名称
        """
        self.team = team
        self.agent_name = agent_name
        self.total_reward = 0.0
        self.game_count = 0
    
    @abstractmethod
    def get_actions(self, game_state: Dict) -> List[Action]:
        """
        根据游戏状态获取所有信使的行动
        
        Args:
            game_state: 当前游戏状态
            
        Returns:
            信使行动列表
        """
        pass
    
    def on_game_end(self, final_score: int, won: bool) -> None:
        """
        游戏结束时的回调
        
        Args:
            final_score: 最终得分
            won: 是否获胜
        """
        self.total_reward += final_score
        self.game_count += 1
        
        if hasattr(self, 'on_episode_end'):
            self.on_episode_end(final_score, won)
    
    def get_average_reward(self) -> float:
        """获取平均奖励"""
        if self.game_count == 0:
            return 0.0
        return self.total_reward / self.game_count
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.total_reward = 0.0
        self.game_count = 0


class RandomAgent(AIAgent):
    """随机行动的AI代理"""
    
    def __init__(self, team: Team):
        super().__init__(team, "RandomAgent")
    
    def get_actions(self, game_state: Dict) -> List[Action]:
        """随机选择行动"""
        actions = []
        team_messengers = game_state['team_messengers']
        
        for messenger_state in team_messengers:
            # 获取可用行动
            available_actions = self._get_available_actions(messenger_state, game_state)
            
            # 随机选择一个行动
            if available_actions:
                action = random.choice(available_actions)
                actions.append(action)
            else:
                # 如果没有可用行动，选择等待
                actions.append(Action(ActionType.WAIT))
        
        return actions
    
    def _get_available_actions(self, messenger_state: Dict, game_state: Dict) -> List[Action]:
        """获取信使的可用行动"""
        actions = []
        x, y = messenger_state['position']
        
        # 移动行动
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # 上下左右
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self._is_valid_position(new_x, new_y, game_state):
                actions.append(Action(ActionType.MOVE, (dx, dy)))
        
        # 咏唱行动
        if messenger_state['can_chant']:
            actions.append(Action(ActionType.CHANT))
        
        # 等待行动
        actions.append(Action(ActionType.WAIT))
        
        return actions
    
    def _is_valid_position(self, x: int, y: int, game_state: Dict) -> bool:
        """检查位置是否有效"""
        grid_size = game_state['environment'].shape[0]
        return 0 <= x < grid_size and 0 <= y < grid_size


class GreedyAgent(AIAgent):
    """贪心策略的AI代理"""
    
    def __init__(self, team: Team):
        super().__init__(team, "GreedyAgent")
    
    def get_actions(self, game_state: Dict) -> List[Action]:
        """使用贪心策略选择行动"""
        actions = []
        team_messengers = game_state['team_messengers']
        environment = game_state['environment']
        
        for messenger_state in team_messengers:
            action = self._get_best_action(messenger_state, game_state)
            actions.append(action)
        
        return actions
    
    def _get_best_action(self, messenger_state: Dict, game_state: Dict) -> Action:
        """为单个信使选择最佳行动"""
        x, y = messenger_state['position']
        environment = game_state['environment']
        
        # 优先级1: 如果当前位置能量值有利，则咏唱
        current_energy = environment[x, y]
        
        if messenger_state['can_chant']:
            if self.team == Team.RED and current_energy >= 0:
                return Action(ActionType.CHANT)
            elif self.team == Team.BLUE and current_energy <= 0:
                return Action(ActionType.CHANT)
        
        # 优先级2: 寻找最有利的相邻位置移动
        best_move = None
        best_score = float('-inf')
        
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self._is_valid_position(new_x, new_y, game_state):
                score = self._evaluate_position(new_x, new_y, environment)
                if score > best_score:
                    best_score = score
                    best_move = (dx, dy)
        
        if best_move:
            return Action(ActionType.MOVE, best_move)
        
        # 如果没有好的移动选择，尝试咏唱或等待
        if messenger_state['can_chant']:
            return Action(ActionType.CHANT)
        
        return Action(ActionType.WAIT)
    
    def _evaluate_position(self, x: int, y: int, environment: np.ndarray) -> float:
        """评估位置的价值"""
        energy = environment[x, y]
        
        if self.team == Team.RED:
            # 红队喜欢能量值高的位置
            return energy
        else:
            # 蓝队喜欢能量值低的位置
            return -energy
    
    def _is_valid_position(self, x: int, y: int, game_state: Dict) -> bool:
        """检查位置是否有效"""
        grid_size = game_state['environment'].shape[0]
        return 0 <= x < grid_size and 0 <= y < grid_size


class HeuristicAgent(AIAgent):
    """启发式策略的AI代理"""
    
    def __init__(self, team: Team):
        super().__init__(team, "HeuristicAgent")
        self.exploration_rate = 0.1  # 探索率
    
    def get_actions(self, game_state: Dict) -> List[Action]:
        """使用启发式策略选择行动"""
        actions = []
        team_messengers = game_state['team_messengers']
        
        for messenger_state in team_messengers:
            if random.random() < self.exploration_rate:
                # 探索：随机行动
                action = self._get_random_action(messenger_state, game_state)
            else:
                # 利用：启发式行动
                action = self._get_heuristic_action(messenger_state, game_state)
            
            actions.append(action)
        
        return actions
    
    def _get_heuristic_action(self, messenger_state: Dict, game_state: Dict) -> Action:
        """启发式行动选择"""
        x, y = messenger_state['position']
        environment = game_state['environment']
        current_turn = game_state['current_turn']
        max_turns = game_state['max_turns']
        
        # 游戏后期更倾向于咏唱
        late_game_threshold = max_turns * 0.7
        
        if current_turn > late_game_threshold and messenger_state['can_chant']:
            current_energy = environment[x, y]
            
            # 在有利位置咏唱
            if (self.team == Team.RED and current_energy >= -2) or \
               (self.team == Team.BLUE and current_energy <= 2):
                return Action(ActionType.CHANT)
        
        # 寻找最佳移动位置
        best_move = self._find_best_move(messenger_state, game_state)
        if best_move:
            return Action(ActionType.MOVE, best_move)
        
        # 如果能咏唱就咏唱
        if messenger_state['can_chant']:
            return Action(ActionType.CHANT)
        
        return Action(ActionType.WAIT)
    
    def _find_best_move(self, messenger_state: Dict, game_state: Dict) -> tuple:
        """寻找最佳移动方向"""
        x, y = messenger_state['position']
        environment = game_state['environment']
        
        best_move = None
        best_score = float('-inf')
        
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self._is_valid_position(new_x, new_y, game_state):
                score = self._evaluate_move(x, y, new_x, new_y, environment)
                if score > best_score:
                    best_score = score
                    best_move = (dx, dy)
        
        return best_move
    
    def _evaluate_move(self, from_x: int, from_y: int, to_x: int, to_y: int, environment: np.ndarray) -> float:
        """评估移动的价值"""
        target_energy = environment[to_x, to_y]
        
        # 考虑周围区域的能量分布
        neighbors_score = 0
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        
        for dx, dy in directions:
            nx, ny = to_x + dx, to_y + dy
            if 0 <= nx < environment.shape[0] and 0 <= ny < environment.shape[1]:
                neighbor_energy = environment[nx, ny]
                if self.team == Team.RED:
                    neighbors_score += neighbor_energy
                else:
                    neighbors_score -= neighbor_energy
        
        # 综合评分
        if self.team == Team.RED:
            return target_energy + neighbors_score * 0.1
        else:
            return -target_energy + neighbors_score * 0.1
    
    def _get_random_action(self, messenger_state: Dict, game_state: Dict) -> Action:
        """获取随机行动"""
        available_actions = self._get_available_actions(messenger_state, game_state)
        return random.choice(available_actions) if available_actions else Action(ActionType.WAIT)
    
    def _get_available_actions(self, messenger_state: Dict, game_state: Dict) -> List[Action]:
        """获取可用行动"""
        actions = []
        x, y = messenger_state['position']
        
        # 移动行动
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]
        for dx, dy in directions:
            new_x, new_y = x + dx, y + dy
            if self._is_valid_position(new_x, new_y, game_state):
                actions.append(Action(ActionType.MOVE, (dx, dy)))
        
        # 咏唱行动
        if messenger_state['can_chant']:
            actions.append(Action(ActionType.CHANT))
        
        # 等待行动
        actions.append(Action(ActionType.WAIT))
        
        return actions
    
    def _is_valid_position(self, x: int, y: int, game_state: Dict) -> bool:
        """检查位置是否有效"""
        grid_size = game_state['environment'].shape[0]
        return 0 <= x < grid_size and 0 <= y < grid_size
